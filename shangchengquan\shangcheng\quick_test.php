<?php
/**
 * 快速测试脚本 - 直接复制这段代码到您的测试环境
 */

// 设置测试参数
$aid = 1;  // 改为您的应用ID
$uid = 1;  // 改为您要测试的用户ID

echo "开始测试重构后的菜单系统...\n\n";

try {
    // 测试新版本菜单
    echo "正在生成菜单...\n";
    $newMenu = \app\common\MenuRefactored::getdata($aid, $uid);
    
    echo "✓ 菜单生成成功！\n";
    echo "菜单项数量: " . count($newMenu) . "\n\n";
    
    // 检查主要菜单
    echo "检查主要菜单:\n";
    $menus = ['shop' => '商城', 'finance' => '财务', 'yingxiao' => '营销', 'system' => '系统'];
    
    foreach ($menus as $key => $name) {
        if (isset($newMenu[$key])) {
            $childCount = isset($newMenu[$key]['child']) ? count($newMenu[$key]['child']) : 0;
            echo "✓ {$name}: {$childCount} 个子菜单\n";
        } else {
            echo "- {$name}: 不存在\n";
        }
    }
    
    // 检查买单收款菜单
    echo "\n检查买单收款菜单:\n";
    $found = false;
    if (isset($newMenu['finance']['child'])) {
        foreach ($newMenu['finance']['child'] as $item) {
            if (strpos($item['name'], '买单') !== false || strpos($item['name'], '收款') !== false) {
                echo "✓ 找到: {$item['name']}\n";
                if (isset($item['child'])) {
                    foreach ($item['child'] as $sub) {
                        echo "  - {$sub['name']}\n";
                    }
                }
                $found = true;
            }
        }
    }
    
    if (!$found) {
        echo "✗ 未找到买单收款菜单\n";
    }
    
    echo "\n测试完成！\n";
    
} catch (Exception $e) {
    echo "✗ 测试失败: " . $e->getMessage() . "\n";
}

// 如果您想看完整的菜单结构，取消下面的注释
// echo "\n完整菜单结构:\n";
// print_r($newMenu);
?>
