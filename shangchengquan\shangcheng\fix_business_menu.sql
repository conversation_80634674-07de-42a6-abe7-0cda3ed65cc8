-- 修复商家后台买单收款菜单显示问题
-- 执行前请备份数据库

-- 1. 检查admin_user表结构，确认是否有type字段
-- 如果没有type字段，需要添加
ALTER TABLE `ddwx_admin_user` ADD COLUMN `type` VARCHAR(20) DEFAULT 'business' COMMENT '用户类型：business-商家，tuanzhang-团长' AFTER `isadmin`;

-- 2. 为所有商家用户（bid > 0）设置正确的用户类型
UPDATE `ddwx_admin_user` SET `type` = 'business' WHERE `bid` > 0 AND (`type` IS NULL OR `type` = '');

-- 3. 检查当前商家用户的设置
SELECT 
    id,
    username,
    bid,
    type,
    isadmin,
    CASE 
        WHEN bid > 0 THEN '商家用户'
        ELSE '平台管理员'
    END as user_role,
    CASE 
        WHEN bid > 0 AND (type = 'business' OR type IS NULL) THEN '应该显示买单菜单'
        WHEN bid = 0 THEN '显示管理员买单菜单'
        ELSE '菜单可能不显示'
    END as menu_status
FROM `ddwx_admin_user` 
WHERE bid > 0 
ORDER BY id;

-- 4. 如果需要，可以手动设置特定用户的类型
-- UPDATE `ddwx_admin_user` SET `type` = 'business' WHERE id = 你的用户ID;
