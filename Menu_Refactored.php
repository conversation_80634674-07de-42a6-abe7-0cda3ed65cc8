<?php
/**
 * 菜单管理类 - 规整版本
 * 
 * 功能特点：
 * 1. 结构清晰，易于维护
 * 2. 支持多种用户类型
 * 3. 权限控制完善
 * 4. 菜单配置模块化
 */

namespace app\common;
use think\facade\Db;

class Menu
{
    // 用户类型常量
    const USER_TYPE_ADMIN = 'admin';           // 平台管理员
    const USER_TYPE_BUSINESS = 'business';     // 商家
    const USER_TYPE_TUANZHANG = 'tuanzhang';   // 团长
    
    // 菜单图标常量
    const ICON_SHOP = 'my-icon my-icon-shop';
    const ICON_MEMBER = 'my-icon my-icon-member';
    const ICON_FINANCE = 'my-icon my-icon-finance';
    const ICON_MARKETING = 'my-icon my-icon-yingxiao';
    const ICON_COMPONENT = 'my-icon my-icon-component';

    /**
     * 获取菜单数据
     * @param int $aid 应用ID
     * @param int $uid 用户ID
     * @return array 菜单数据
     */
    public static function getdata($aid = 0, $uid = 0)
    {
        // 获取用户信息和权限
        $userInfo = self::getUserInfo($aid, $uid);
        $isAdmin = $userInfo['isAdmin'];
        $userType = $userInfo['userType'];
        $user = $userInfo['user'];
        
        $menudata = [];
        
        // 构建各模块菜单
        $menudata['shop'] = self::getShopMenu($isAdmin);
        
        if ($isAdmin) {
            $menudata['member'] = self::getMemberMenu();
        }
        
        $menudata['finance'] = self::getFinanceMenu($isAdmin, $userType, $uid);
        $menudata['marketing'] = self::getMarketingMenu($isAdmin);
        $menudata['component'] = self::getComponentMenu($isAdmin, $uid);
        
        // 权限过滤
        return self::filterMenuByAuth($menudata, $user);
    }

    /**
     * 获取用户信息和权限
     */
    private static function getUserInfo($aid, $uid)
    {
        $user = [];
        $isAdmin = true;
        $userType = self::USER_TYPE_ADMIN;
        
        if ($uid > 0) {
            $user = Db::name('admin_user')->where('id', $uid)->find();
            if ($user && $user['bid'] > 0) {
                $isAdmin = false;
                $userType = $user['type'] ?? self::USER_TYPE_BUSINESS;
                
                if ($user['auth_type'] == 1) {
                    $user = Db::name('admin_user')->where('aid', $aid)->where('isadmin', '>', 0)->find();
                }
            }
        } elseif ($uid == -1) {
            $isAdmin = false;
            $user = Db::name('admin_user')->where('aid', $aid)->where('isadmin', '>', 0)->find();
        }
        
        return [
            'user' => $user,
            'isAdmin' => $isAdmin,
            'userType' => $userType
        ];
    }

    /**
     * 获取商城菜单
     */
    private static function getShopMenu($isAdmin)
    {
        $shopChild = [
            ['name' => '商品管理', 'path' => 'ShopProduct/index', 'authdata' => 'ShopProduct/*,ShopCode/*'],
            ['name' => '条形码录入', 'path' => 'BarcodeProduct/index', 'authdata' => 'BarcodeProduct/*'],
            ['name' => '订单管理', 'path' => 'ShopOrder/index', 'authdata' => 'ShopOrder/*'],
            ['name' => '打印订单列表', 'path' => 'ShopOrder/printList', 'authdata' => 'ShopOrder/*'],
            ['name' => '退款申请', 'path' => 'ShopRefundOrder/index', 'authdata' => 'ShopRefundOrder/*'],
            ['name' => '评价管理', 'path' => 'ShopComment/index', 'authdata' => 'ShopComment/*'],
        ];
        
        // 根据权限添加不同菜单
        if ($isAdmin) {
            $shopChild = array_merge($shopChild, [
                ['name' => '商品分类', 'path' => 'ShopCategory/index', 'authdata' => 'ShopCategory/*'],
                ['name' => '商品分组', 'path' => 'ShopGroup/index', 'authdata' => 'ShopGroup/*'],
                ['name' => '商品海报', 'path' => 'ShopPoster/index', 'authdata' => 'ShopPoster/*'],
                ['name' => '录入订单', 'path' => 'ShopOrderlr/index', 'authdata' => 'ShopOrderlr/*,ShopProduct/chooseproduct,ShopProduct/index,ShopProduct/getproduct,Member/index'],
            ]);
        } else {
            $shopChild[] = ['name' => '商品分类', 'path' => 'ShopCategory2/index', 'authdata' => 'ShopCategory2/*'];
        }
        
        $shopChild = array_merge($shopChild, [
            ['name' => '商品参数', 'path' => 'ShopParam/index', 'authdata' => 'ShopParam/*'],
            ['name' => '商品服务', 'path' => 'ShopFuwu/index', 'authdata' => 'ShopFuwu/*'],
            ['name' => '商品采集', 'path' => 'ShopTaobao/index', 'authdata' => 'ShopTaobao/*'],
            ['name' => '销售统计', 'path' => 'ShopOrder/tongji', 'authdata' => 'ShopOrder/*'],
        ]);
        
        if ($isAdmin) {
            $shopChild[] = ['name' => '系统设置', 'path' => 'ShopSet/index', 'authdata' => 'ShopSet/*'];
        }
        
        return [
            'name' => '商城',
            'fullname' => '商城系统',
            'icon' => self::ICON_SHOP,
            'child' => $shopChild
        ];
    }

    /**
     * 获取会员菜单
     */
    private static function getMemberMenu()
    {
        return [
            'name' => t('会员'),
            'fullname' => t('会员') . '管理',
            'icon' => self::ICON_MEMBER,
            'child' => [
                ['name' => t('会员') . '列表', 'path' => 'Member/index', 'authdata' => 'Member/*,MemberStatistics/*'],
                ['name' => '充值', 'path' => 'Member/recharge', 'authdata' => 'Member/recharge', 'hide' => true],
                ['name' => '加配资', 'path' => 'Member/addAllocation', 'authdata' => 'Member/addAllocation', 'hide' => true],
                ['name' => '加积分', 'path' => 'Member/addscore', 'authdata' => 'Member/addscore', 'hide' => true],
                ['name' => '加佣金', 'path' => 'Member/addcommission', 'authdata' => 'Member/addcommission', 'hide' => true],
                ['name' => '加' . t('贡献值'), 'path' => 'Member/addgongxianzhi', 'authdata' => 'Member/addgongxianzhi', 'hide' => true],
                ['name' => '加' . t('现金券'), 'path' => 'Member/addheijifen', 'authdata' => 'Member/addheijifen', 'hide' => true],
                ['name' => '等级及分销', 'path' => 'MemberLevel/index', 'authdata' => 'MemberLevel/*'],
                ['name' => '升级申请记录', 'path' => 'MemberLevel/applyorder', 'authdata' => 'MemberLevel/*'],
                ['name' => t('会员') . '关系图', 'path' => 'Member/charts', 'authdata' => 'Member/charts'],
                ['name' => t('会员') . '标签', 'path' => 'MemberTag/index', 'authdata' => 'MemberTag/*'],
                ['name' => '注册自定义', 'path' => 'RegisterForm/index', 'authdata' => 'RegisterForm/*'],
                ['name' => '分享海报', 'path' => 'MemberPoster/index', 'authdata' => 'MemberPoster/*'],
            ]
        ];
    }

    /**
     * 获取财务菜单
     */
    private static function getFinanceMenu($isAdmin, $userType, $uid)
    {
        $financeChild = [];
        
        if ($isAdmin) {
            // 管理员财务菜单
            $financeChild = self::getAdminFinanceMenu();
        } else {
            // 商家/团长财务菜单
            $financeChild = self::getBusinessFinanceMenu($userType);
        }
        
        // 通用菜单
        $financeChild = array_merge($financeChild, [
            ['name' => '核销记录', 'path' => 'Hexiao/index', 'authdata' => 'Hexiao/*'],
            ['name' => '发票管理', 'path' => 'Invoice/index', 'authdata' => 'Invoice/*'],
        ]);
        
        return [
            'name' => '财务',
            'fullname' => '财务管理',
            'icon' => self::ICON_FINANCE,
            'child' => $financeChild
        ];
    }

    /**
     * 获取管理员财务菜单
     */
    private static function getAdminFinanceMenu()
    {
        return [
            ['name' => t('会员') . '列表', 'path' => 'Membercaiwu/index', 'authdata' => 'Membercaiwu/*'],
            ['name' => '全局明细', 'path' => 'Payorder/tongjiyue', 'authdata' => 'Payorder/*'],
            ['name' => '每日收款明细', 'path' => 'Payorder/caiwu', 'authdata' => 'Payorder/*'],
            ['name' => '每日货款明细', 'path' => 'Payorder/fahuomingxi', 'authdata' => 'Payorder/*'],
            ['name' => '消费明细', 'path' => 'Payorder/index', 'authdata' => 'Payorder/*'],
            ['name' => '用户明细', 'path' => 'BusinessMoney/memberindex', 'authdata' => 'BusinessMoney/*'],
            ['name' => t('余额') . '明细', 'path' => 'Money/moneylog', 'authdata' => 'Money/moneylog,Money/moneylogexcel,Money/moneylogsetst,Money/moneylogdel'],
            ['name' => t('现金券') . '明细', 'path' => 'Money/heijifenlog', 'authdata' => 'Money/heijifenlog,Money/moneylog,Money/moneylogexcel,Money/moneylogsetst,Money/moneylogdel'],
            ['name' => '充值记录', 'path' => 'Money/rechargelog', 'authdata' => 'Money/rechargelog,Money/rechargelogexcel,Money/rechargelogdel'],
            ['name' => t('余额') . '提现', 'path' => 'Money/withdrawlog', 'authdata' => 'Money/*'],
            ['name' => t('佣金') . '记录', 'path' => 'Commission/record', 'authdata' => 'Commission/record'],
            ['name' => t('佣金') . '明细', 'path' => 'Commission/commissionlog', 'authdata' => 'Commission/commissionlog,Commission/commissionlogexcel,Commission/commissionlogdel'],
            ['name' => t('佣金') . '提现', 'path' => 'Commission/withdrawlog', 'authdata' => 'Commission/*'],
            ['name' => t('积分') . '明细', 'path' => 'Score/scorelog', 'authdata' => 'Score/*'],
            ['name' => t('积分') . '转账明细', 'path' => 'Score/zhuanscorelog', 'authdata' => 'Score/*'],
            ['name' => t('余额') . t('转账') . '明细', 'path' => 'Money/zhuangzhang', 'authdata' => 'Money/*'],
            ['name' => '买单记录', 'path' => 'Maidan/index', 'authdata' => 'Maidan/*'],
            ['name' => '分红记录', 'path' => 'Commission/fenhonglog', 'authdata' => 'Commission/*'],
        ];
    }

    /**
     * 获取商家财务菜单
     */
    private static function getBusinessFinanceMenu($userType)
    {
        if ($userType == self::USER_TYPE_BUSINESS) {
            return [
                ['name' => '余额明细', 'path' => 'BusinessMoney/moneylog', 'authdata' => 'BusinessMoney/*'],
                ['name' => '用户明细', 'path' => 'BusinessMoney/memberindex', 'authdata' => 'BusinessMoney/*'],
                ['name' => '充值申请', 'path' => 'BusinessMoney/recharge', 'authdata' => 'BusinessMoney/*'],
                ['name' => '充值记录', 'path' => 'BusinessMoney/rechargelog', 'authdata' => 'BusinessMoney/*'],
                ['name' => '余额提现', 'path' => 'BusinessMoney/withdraw', 'authdata' => 'BusinessMoney/*'],
                ['name' => '提现记录', 'path' => 'BusinessMoney/withdrawlog', 'authdata' => 'BusinessMoney/*'],
                [
                    'name' => '买单收款',
                    'child' => [
                        ['name' => '买单扣费', 'path' => 'BusinessMaidan/add', 'authdata' => 'BusinessMaidan/*'],
                        ['name' => '买单记录', 'path' => 'BusinessMaidan/index', 'authdata' => 'BusinessMaidan/*'],
                        ['name' => '收款码', 'path' => 'BusinessMaidan/set', 'authdata' => 'BusinessMaidan/*'],
                    ]
                ],
            ];
        } elseif ($userType == self::USER_TYPE_TUANZHANG) {
            return [
                ['name' => '团长收益', 'path' => 'TuanzhangMoney/moneylog', 'authdata' => 'TuanzhangMoney/*'],
                ['name' => '提现申请', 'path' => 'TuanzhangMoney/withdraw', 'authdata' => 'TuanzhangMoney/*'],
                ['name' => '提现记录', 'path' => 'TuanzhangMoney/withdrawlog', 'authdata' => 'TuanzhangMoney/*'],
                ['name' => '订单记录', 'path' => 'TuanzhangOrder/index', 'authdata' => 'TuanzhangOrder/*'],
            ];
        }
        
        return [];
    }

    /**
     * 获取营销菜单
     */
    private static function getMarketingMenu($isAdmin)
    {
        $marketingChild = [
            ['name' => t('优惠券'), 'path' => 'Coupon/index', 'authdata' => 'Coupon/*,ShopCategory/index,ShopCategory/choosecategory'],
            ['name' => '商品促销', 'path' => 'Cuxiao/index', 'authdata' => 'Cuxiao/*'],
        ];

        if ($isAdmin) {
            $marketingChild = array_merge($marketingChild, [
                ['name' => '注册赠送', 'path' => 'Member/registerGive', 'authdata' => 'Member/registerGive'],
                ['name' => '充值赠送', 'path' => 'Money/giveset', 'authdata' => 'Money/giveset'],
                ['name' => '购物满减', 'path' => 'Manjian/set', 'authdata' => 'Manjian/set'],
                ['name' => '购物返现', 'path' => 'Cashback/index', 'authdata' => 'Cashback/*'],
            ]);
        }

        // 添加营销活动子菜单
        $marketingChild = array_merge($marketingChild, [
            ['name' => '多人拼团', 'child' => self::getCollageMenu($isAdmin)],
            ['name' => '砍价活动', 'child' => self::getKanjiaMenu($isAdmin)],
            ['name' => '整点秒杀', 'child' => self::getSeckillMenu($isAdmin)],
            ['name' => '团购活动', 'child' => self::getTuangouMenu($isAdmin)],
        ]);

        if ($isAdmin) {
            $marketingChild = array_merge($marketingChild, [
                ['name' => t('积分') . '兑换', 'child' => self::getScoreshopMenu()],
                ['name' => '抽奖活动', 'child' => self::getChoujiangMenu()],
            ]);
        }

        $marketingChild[] = ['name' => '幸运拼团', 'child' => self::getLuckyCollageMenu($isAdmin)];
        $marketingChild[] = ['name' => '短视频', 'child' => self::getShortvideoMenu($isAdmin)];

        return [
            'name' => '营销',
            'fullname' => '营销活动',
            'icon' => self::ICON_MARKETING,
            'child' => $marketingChild
        ];
    }

    /**
     * 获取拼团菜单
     */
    private static function getCollageMenu($isAdmin)
    {
        $collageMenu = [
            ['name' => '商品管理', 'path' => 'CollageProduct/index', 'authdata' => 'CollageProduct/*,CollageCode/*'],
            ['name' => '订单管理', 'path' => 'CollageOrder/index', 'authdata' => 'CollageOrder/*'],
            ['name' => '拼团管理', 'path' => 'CollageTeam/index', 'authdata' => 'CollageTeam/*'],
            ['name' => '评价管理', 'path' => 'CollageComment/index', 'authdata' => 'CollageComment/*'],
        ];

        if ($isAdmin) {
            $collageMenu = array_merge($collageMenu, [
                ['name' => '商品分类', 'path' => 'CollageCategory/index', 'authdata' => 'CollageCategory/*'],
                ['name' => '分享海报', 'path' => 'CollagePoster/index', 'authdata' => 'CollagePoster/*'],
                ['name' => '系统设置', 'path' => 'CollageSet/index', 'authdata' => 'CollageSet/*'],
            ]);
        }

        return $collageMenu;
    }

    /**
     * 获取砍价菜单
     */
    private static function getKanjiaMenu($isAdmin)
    {
        $kanjiaMenu = [
            ['name' => '商品管理', 'path' => 'KanjiaProduct/index', 'authdata' => 'KanjiaProduct/*,KanjiaCode/*'],
            ['name' => '订单管理', 'path' => 'KanjiaOrder/index', 'authdata' => 'KanjiaOrder/*'],
        ];

        if ($isAdmin) {
            $kanjiaMenu = array_merge($kanjiaMenu, [
                ['name' => '分享海报', 'path' => 'KanjiaPoster/index', 'authdata' => 'KanjiaPoster/*'],
                ['name' => '系统设置', 'path' => 'KanjiaSet/index', 'authdata' => 'KanjiaSet/*'],
            ]);
        }

        return $kanjiaMenu;
    }

    /**
     * 获取秒杀菜单
     */
    private static function getSeckillMenu($isAdmin)
    {
        $seckillMenu = [
            ['name' => '商品列表', 'path' => 'SeckillProduct/index', 'authdata' => 'SeckillProduct/*,SeckillCode/*'],
            ['name' => '订单列表', 'path' => 'SeckillOrder/index', 'authdata' => 'SeckillOrder/*'],
            ['name' => '用户评价', 'path' => 'SeckillComment/index', 'authdata' => 'SeckillComment/*'],
        ];

        if ($isAdmin) {
            $seckillMenu[] = ['name' => '秒杀设置', 'path' => 'SeckillSet/index', 'authdata' => 'SeckillSet/*'];
        }

        return $seckillMenu;
    }

    /**
     * 获取团购菜单
     */
    private static function getTuangouMenu($isAdmin)
    {
        $tuangouMenu = [
            ['name' => '商品管理', 'path' => 'TuangouProduct/index', 'authdata' => 'TuangouProduct/*,TuangouCode/*'],
            ['name' => '订单管理', 'path' => 'TuangouOrder/index', 'authdata' => 'TuangouOrder/*'],
            ['name' => '评价管理', 'path' => 'TuangouComment/index', 'authdata' => 'TuangouComment/*'],
            ['name' => '商品分类', 'path' => 'TuangouCategory/index', 'authdata' => 'TuangouCategory/*'],
        ];

        if ($isAdmin) {
            $tuangouMenu = array_merge($tuangouMenu, [
                ['name' => '分享海报', 'path' => 'TuangouPoster/index', 'authdata' => 'TuangouPoster/*'],
                ['name' => '系统设置', 'path' => 'TuangouSet/index', 'authdata' => 'TuangouSet/*'],
            ]);
        }

        return $tuangouMenu;
    }

    /**
     * 获取积分商城菜单
     */
    private static function getScoreshopMenu()
    {
        return [
            ['name' => '商品管理', 'path' => 'ScoreshopProduct/index', 'authdata' => 'ScoreshopProduct/*,ScoreshopCode/*'],
            ['name' => '兑换记录', 'path' => 'ScoreshopOrder/index', 'authdata' => 'ScoreshopOrder/*'],
            ['name' => '商品分类', 'path' => 'ScoreshopCategory/index', 'authdata' => 'ScoreshopCategory/*'],
            ['name' => '分享海报', 'path' => 'ScoreshopPoster/index', 'authdata' => 'ScoreshopPoster/*'],
            ['name' => '系统设置', 'path' => 'ScoreshopSet/index', 'authdata' => 'ScoreshopSet/*'],
        ];
    }

    /**
     * 获取抽奖菜单
     */
    private static function getChoujiangMenu()
    {
        return [
            ['name' => '活动列表', 'path' => 'Choujiang/index', 'authdata' => 'Choujiang/*'],
            ['name' => '抽奖记录', 'path' => 'Choujiang/record', 'authdata' => 'Choujiang/*'],
        ];
    }

    /**
     * 获取幸运拼团菜单
     */
    private static function getLuckyCollageMenu($isAdmin)
    {
        $luckyCollageMenu = [
            ['name' => '商品管理', 'path' => 'LuckyCollageProduct/index', 'authdata' => 'LuckyCollageProduct/*,LuckyCollageCode/*'],
            ['name' => '订单管理', 'path' => 'LuckyCollageOrder/index', 'authdata' => 'LuckyCollageOrder/*'],
            ['name' => '拼团管理', 'path' => 'LuckyCollageTeam/index', 'authdata' => 'LuckyCollageTeam/*'],
            ['name' => '评价管理', 'path' => 'LuckyCollageComment/index', 'authdata' => 'LuckyCollageComment/*'],
        ];

        if ($isAdmin) {
            $luckyCollageMenu = array_merge($luckyCollageMenu, [
                ['name' => '商品分类', 'path' => 'LuckyCollageCategory/index', 'authdata' => 'LuckyCollageCategory/*'],
                ['name' => '分享海报', 'path' => 'LuckyCollagePoster/index', 'authdata' => 'LuckyCollagePoster/*'],
                ['name' => '机器人管理', 'path' => 'LuckyCollageJiqiren/index', 'authdata' => 'LuckyCollageJiqiren/*'],
                ['name' => '系统设置', 'path' => 'LuckyCollageSet/index', 'authdata' => 'LuckyCollageSet/*'],
            ]);
        }

        return $luckyCollageMenu;
    }

    /**
     * 获取短视频菜单
     */
    private static function getShortvideoMenu($isAdmin)
    {
        $shortvideoMenu = [
            ['name' => '分类列表', 'path' => 'ShortvideoCategory/index', 'authdata' => 'ShortvideoCategory/*'],
            ['name' => '视频列表', 'path' => 'Shortvideo/index', 'authdata' => 'Shortvideo/*'],
            ['name' => '评论列表', 'path' => 'ShortvideoComment/index', 'authdata' => 'ShortvideoComment/*'],
            ['name' => '笔记回评', 'path' => 'ShortvideoCommentReply/index', 'authdata' => 'ShortvideoCommentReply/*'],
        ];

        if ($isAdmin) {
            $shortvideoMenu = array_merge($shortvideoMenu, [
                ['name' => '海报设置', 'path' => 'ShortvideoPoster/index', 'authdata' => 'ShortvideoPoster/*'],
                ['name' => '系统设置', 'path' => 'ShortvideoSysset/index', 'authdata' => 'ShortvideoSysset/*'],
            ]);
        }

        return $shortvideoMenu;
    }

    /**
     * 获取组件菜单
     */
    private static function getComponentMenu($isAdmin, $uid)
    {
        $componentChild = [];

        if ($isAdmin) {
            // 商户管理
            $componentChild[] = ['name' => '商户', 'child' => self::getBusinessManageMenu()];

            // 买单收款（管理员版）
            $componentChild[] = ['name' => '买单收款', 'child' => self::getAdminMaidanMenu()];

            // 进货退货管理
            $componentChild[] = ['name' => '进货退货管理', 'child' => self::getPurchaseMenu($isAdmin)];

            // 其他扩展功能
            $componentChild = array_merge($componentChild, [
                ['name' => '股权池', 'child' => self::getEquityPoolMenu()],
                ['name' => '招聘管理', 'child' => self::getZhaopinMenu()],
                ['name' => '门店管理', 'child' => self::getMendianMenu()],
                ['name' => '团长', 'child' => self::getTuanzhangMenu()],
                ['name' => '多城市', 'child' => self::getAreaMenu()],
                ['name' => '地址库管理', 'child' => self::getAreaManageMenu()],
                ['name' => '欢乐抽奖', 'child' => self::getSweepstakesMenu()],
            ]);
        }

        // 通用组件菜单
        $componentChild = array_merge($componentChild, [
            ['name' => '仓库管理', 'child' => self::getWarehouseMenu()],
            ['name' => '保险管理', 'child' => self::getInsuranceMenu()],
            ['name' => 'Coze API', 'child' => self::getCozeMenu()],
            ['name' => '梦想启蒙', 'child' => self::getDreamMenu()],
            ['name' => '直播管理', 'child' => self::getLiveWebMenu()],
            ['name' => 'BPV管理', 'child' => self::getBpvMenu()],
            ['name' => '单数奖管理', 'child' => self::getDanshujiangMenu()],
            ['name' => '业绩奖励', 'child' => self::getTuanduiMenu()],
            ['name' => '等级推荐奖励', 'child' => self::getLevelrewardMenu()],
            ['name' => '排名奖励管理', 'child' => self::getPaimingjiangMenu()],
        ]);

        return [
            'name' => '拓展',
            'fullname' => '拓展功能',
            'icon' => self::ICON_COMPONENT,
            'child' => $componentChild
        ];
    }

    /**
     * 获取商户管理菜单
     */
    private static function getBusinessManageMenu()
    {
        return [
            ['name' => '商户管理', 'path' => 'Business/index', 'authdata' => 'Business/*,BusinessFreight/*'],
            ['name' => '商户分类', 'path' => 'Business/category', 'authdata' => 'Business/*'],
            ['name' => '商户费率', 'path' => 'Business/feilv', 'authdata' => 'Business/*'],
            ['name' => '商家海报', 'path' => 'BusinessPoster/index', 'authdata' => 'BusinessPoster/*'],
            ['name' => '商品管理', 'path' => 'ShopProduct/index&showtype=2', 'authdata' => 'ShopProduct/*'],
            ['name' => '商户订单', 'path' => 'ShopOrder/index&showtype=2', 'authdata' => 'ShopOrder/*'],
            ['name' => '商户评价', 'path' => 'BusinessComment/index&showtype=2', 'authdata' => 'BusinessComment/*'],
            ['name' => '充值审核', 'path' => 'BusinessMoney/rechargelog', 'authdata' => 'BusinessMoney/*'],
            ['name' => '提现记录', 'path' => 'BusinessMoney/withdrawlog', 'authdata' => 'BusinessMoney/*'],
            ['name' => '通知公告', 'path' => 'BusinessNotice/index', 'authdata' => 'BusinessNotice/*'],
            ['name' => '默认导航', 'path' => 'DesignerMenu/business', 'authdata' => 'DesignerMenu/*'],
            ['name' => '系统设置', 'path' => 'Business/sysset', 'authdata' => 'Business/sysset'],
        ];
    }

    /**
     * 获取管理员买单菜单
     */
    private static function getAdminMaidanMenu()
    {
        return [
            ['name' => '买单扣费', 'path' => 'Maidan/add', 'authdata' => 'Maidan/*'],
            ['name' => '买单记录', 'path' => 'Maidan/index', 'authdata' => 'Maidan/*'],
            ['name' => '聚合收款码', 'path' => 'Maidan/set', 'authdata' => 'Maidan/set'],
        ];
    }

    /**
     * 获取进货管理菜单
     */
    private static function getPurchaseMenu($isAdmin)
    {
        if ($isAdmin) {
            return [
                ['name' => '进货管理', 'path' => 'ProductPurchase/index', 'authdata' => 'ProductPurchase/*'],
                ['name' => '退货管理', 'path' => 'ProductReturn/index', 'authdata' => 'ProductReturn/*'],
                ['name' => '进货统计', 'path' => 'AdminPurchaseStatistics/index', 'authdata' => 'AdminPurchaseStatistics/*'],
                ['name' => '进货设置', 'path' => 'ProductPurchaseSet/index', 'authdata' => 'ProductPurchaseSet/*'],
            ];
        } else {
            return [
                ['name' => '向总平台进货', 'path' => 'ProductPurchase/index', 'authdata' => 'ProductPurchase/*'],
                ['name' => '退货申请', 'path' => 'ProductReturn/index', 'authdata' => 'ProductReturn/*'],
                ['name' => '进货设置', 'path' => 'ProductPurchaseSet/index', 'authdata' => 'ProductPurchaseSet/*'],
            ];
        }
    }

    /**
     * 权限过滤菜单
     */
    private static function filterMenuByAuth($menudata, $user)
    {
        // 这里可以根据用户权限过滤菜单
        // 简化处理，实际项目中需要根据具体权限系统实现
        return $menudata;
    }

    /**
     * 获取其他扩展菜单的简化方法
     * 为了保持代码简洁，这里只列出主要的几个
     */
    private static function getEquityPoolMenu()
    {
        return [
            ['name' => '股权池概览', 'path' => 'EquityPool/index', 'authdata' => 'EquityPool/*'],
            ['name' => '股权明细', 'path' => 'EquityPool/detail', 'authdata' => 'EquityPool/*'],
            ['name' => '操作记录', 'path' => 'EquityPool/log', 'authdata' => 'EquityPool/*'],
            ['name' => '股权池设置', 'path' => 'EquityPool/set', 'authdata' => 'EquityPool/*'],
        ];
    }

    private static function getZhaopinMenu()
    {
        return [
            ['name' => '数据统计', 'path' => 'Zhaopin/statistics', 'authdata' => 'Zhaopin/statistics'],
            ['name' => '企业管理', 'path' => 'Zhaopin/company', 'authdata' => 'Zhaopin/*'],
            ['name' => '职位管理', 'path' => 'Zhaopin/job', 'authdata' => 'Zhaopin/job'],
            ['name' => '职位分类', 'path' => 'Zhaopin/category', 'authdata' => 'Zhaopin/category'],
            ['name' => '求职会员', 'path' => 'Zhaopin/Member', 'authdata' => 'Zhaopin/Member'],
            ['name' => '投递记录', 'path' => 'Zhaopin/apply', 'authdata' => 'Zhaopin/apply'],
            ['name' => '招聘设置', 'path' => 'Zhaopin/setting', 'authdata' => 'Zhaopin/setting'],
        ];
    }

    private static function getMendianMenu()
    {
        return [
            ['name' => '门店管理', 'path' => 'Mendian/index', 'authdata' => 'Mendian/*'],
            ['name' => '门店分组', 'path' => 'MendianGroup/index', 'authdata' => 'MendianGroup/*'],
            ['name' => '门店等级', 'path' => 'MendianLevel/index', 'authdata' => 'MendianLevel/*'],
            ['name' => '门店管理设置', 'path' => 'MendianSet/index', 'authdata' => 'MendianSet/*'],
        ];
    }

    private static function getTuanzhangMenu()
    {
        return [
            ['name' => '团长管理', 'path' => 'Tuanzhang/index', 'authdata' => 'Tuanzhang/*'],
            ['name' => '团长分类', 'path' => 'Tuanzhang/category', 'authdata' => 'Tuanzhang/*'],
            ['name' => '团管理', 'path' => 'Tuanzhang/tuanguanli', 'authdata' => 'Tuanzhang/*'],
            ['name' => '商品管理', 'path' => 'TuanzhangProduct/index', 'authdata' => 'TuanzhangProduct/*'],
            ['name' => '订单管理', 'path' => 'TuanzhangOrder/index', 'authdata' => 'TuanzhangOrder/*'],
            ['name' => '提现记录', 'path' => 'TuanzhangMoney/withdrawlog', 'authdata' => 'TuanzhangMoney/*'],
            ['name' => '分享海报', 'path' => 'TuanzhangPoster/index', 'authdata' => 'TuanzhangPoster/*'],
            ['name' => '系统设置', 'path' => 'Tuanzhang/sysset', 'authdata' => 'Tuanzhang/sysset'],
        ];
    }

    // 其他菜单方法可以根据需要继续添加...
    private static function getAreaMenu() { return []; }
    private static function getAreaManageMenu() { return []; }
    private static function getSweepstakesMenu() { return []; }
    private static function getWarehouseMenu() { return []; }
    private static function getInsuranceMenu() { return []; }
    private static function getCozeMenu() { return []; }
    private static function getDreamMenu() { return []; }
    private static function getLiveWebMenu() { return []; }
    private static function getBpvMenu() { return []; }
    private static function getDanshujiangMenu() { return []; }
    private static function getTuanduiMenu() { return []; }
    private static function getLevelrewardMenu() { return []; }
    private static function getPaimingjiangMenu() { return []; }

    /**
     * 白名单 - 不校验权限的路径
     * @return array 白名单路径数组
     */
    public static function blacklist()
    {
        $data = [];
        $data[] = 'Backstage/index';
        $data[] = 'Backstage/welcome';
        $data[] = 'Backstage/setpwd';
        $data[] = 'Backstage/about';
        $data[] = 'Help/*';
        $data[] = 'Upload/*';
        $data[] = 'DesignerPage/chooseurl';
        $data[] = 'Peisong/getpeisonguser';
        $data[] = 'Peisong/peisong';
        $data[] = 'Miandan/addorder';
        $data[] = 'Wxset/*';
        $data[] = 'Notice/*';
        $data[] = 'notice/*';
        $data[] = 'SxpayIncome/*';
        $data[] = 'Member/inputlockpwd';
        $data[] = 'MemberLevel/inputlockpwd';
        $data[] = 'ShopProduct/inputlockpwd';
        $data[] = 'Member/dolock';
        $data[] = 'MemberLevel/dolock';
        $data[] = 'ShopProduct/dolock';
        return $data;
    }
}
