<?php
/**
 * 简化的菜单测试脚本
 * 直接测试重构后的菜单系统
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>菜单系统测试</h1>";

// 测试参数 - 您可以修改这些值
$aid = 1;  // 应用ID
$uid = 1;  // 用户ID - 改为您要测试的用户ID

try {
    echo "<h2>测试配置</h2>";
    echo "应用ID (aid): {$aid}<br>";
    echo "用户ID (uid): {$uid}<br>";
    echo "<hr>";
    
    echo "<h2>1. 测试重构版本菜单</h2>";
    
    // 检查类是否存在
    if (!class_exists('\app\common\MenuRefactored')) {
        echo "<p style='color: red;'>错误: MenuRefactored 类不存在，请确保文件已正确上传</p>";
        exit;
    }
    
    if (!class_exists('\app\common\MenuConfig')) {
        echo "<p style='color: red;'>错误: MenuConfig 类不存在，请确保文件已正确上传</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✓ 重构类文件加载成功</p>";
    
    // 测试菜单生成
    echo "<h3>生成菜单数据...</h3>";
    $startTime = microtime(true);
    
    $newMenu = \app\common\MenuRefactored::getdata($aid, $uid);
    
    $endTime = microtime(true);
    $executionTime = ($endTime - $startTime) * 1000;
    
    echo "<p>菜单生成耗时: " . number_format($executionTime, 2) . " 毫秒</p>";
    echo "<p>菜单项数量: " . count($newMenu) . "</p>";
    
    echo "<h3>菜单结构检查</h3>";
    
    // 检查主要菜单
    $expectedMenus = [
        'shop' => '商城',
        'finance' => '财务',
        'yingxiao' => '营销',
        'system' => '系统'
    ];
    
    foreach ($expectedMenus as $key => $name) {
        if (isset($newMenu[$key])) {
            echo "<p style='color: green;'>✓ {$name}菜单存在</p>";
            if (isset($newMenu[$key]['child'])) {
                echo "<p>&nbsp;&nbsp;子菜单数量: " . count($newMenu[$key]['child']) . "</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠ {$name}菜单不存在（可能因权限限制）</p>";
        }
    }
    
    // 特别检查买单收款菜单
    echo "<h3>买单收款菜单检查</h3>";
    $foundMaidanMenu = false;
    
    if (isset($newMenu['finance']['child'])) {
        foreach ($newMenu['finance']['child'] as $item) {
            if (strpos($item['name'], '买单') !== false || strpos($item['name'], '收款') !== false) {
                echo "<p style='color: green;'>✓ 找到买单相关菜单: {$item['name']}</p>";
                if (isset($item['child'])) {
                    echo "<p>&nbsp;&nbsp;子菜单项:</p>";
                    echo "<ul>";
                    foreach ($item['child'] as $subItem) {
                        echo "<li>{$subItem['name']} ({$subItem['path']})</li>";
                    }
                    echo "</ul>";
                }
                $foundMaidanMenu = true;
            }
        }
    }
    
    if (!$foundMaidanMenu) {
        echo "<p style='color: red;'>✗ 未找到买单收款菜单</p>";
        echo "<p>可能原因：</p>";
        echo "<ul>";
        echo "<li>用户类型不是商家 (需要 bid > 0)</li>";
        echo "<li>用户类型字段不是 'business'</li>";
        echo "<li>权限配置问题</li>";
        echo "</ul>";
    }
    
    echo "<h2>2. 测试权限白名单</h2>";
    $blacklist = \app\common\MenuRefactored::blacklist();
    echo "<p>白名单项数量: " . count($blacklist) . "</p>";
    echo "<p>前5项: " . implode(', ', array_slice($blacklist, 0, 5)) . "</p>";
    
    echo "<h2>3. 详细菜单数据</h2>";
    echo "<details>";
    echo "<summary>点击查看完整菜单结构</summary>";
    echo "<pre>";
    print_r($newMenu);
    echo "</pre>";
    echo "</details>";
    
    echo "<h2>4. 对比测试（如果原版本存在）</h2>";
    if (class_exists('\app\common\Menu')) {
        try {
            $oldMenu = \app\common\Menu::getdata($aid, $uid);
            echo "<p style='color: green;'>✓ 原版本菜单生成成功</p>";
            echo "<p>原版本菜单项数量: " . count($oldMenu) . "</p>";
            echo "<p>新版本菜单项数量: " . count($newMenu) . "</p>";
            
            // 简单对比
            $oldKeys = array_keys($oldMenu);
            $newKeys = array_keys($newMenu);
            $missingKeys = array_diff($oldKeys, $newKeys);
            $extraKeys = array_diff($newKeys, $oldKeys);
            
            if (empty($missingKeys) && empty($extraKeys)) {
                echo "<p style='color: green;'>✓ 菜单键名完全匹配</p>";
            } else {
                if (!empty($missingKeys)) {
                    echo "<p style='color: orange;'>缺失的菜单: " . implode(', ', $missingKeys) . "</p>";
                }
                if (!empty($extraKeys)) {
                    echo "<p style='color: blue;'>新增的菜单: " . implode(', ', $extraKeys) . "</p>";
                }
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>原版本测试失败: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: orange;'>原版本Menu类不存在，跳过对比测试</p>";
    }
    
    echo "<h2>测试结果</h2>";
    echo "<p style='color: green; font-size: 18px; font-weight: bold;'>✓ 菜单系统重构测试完成！</p>";
    
} catch (Exception $e) {
    echo "<h2>测试失败</h2>";
    echo "<p style='color: red;'>错误信息: " . $e->getMessage() . "</p>";
    echo "<p>错误文件: " . $e->getFile() . " 第 " . $e->getLine() . " 行</p>";
    echo "<details>";
    echo "<summary>详细错误信息</summary>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</details>";
}

echo "<hr>";
echo "<h2>使用说明</h2>";
echo "<ol>";
echo "<li>修改上面的 \$aid 和 \$uid 变量为您要测试的值</li>";
echo "<li>确保 MenuRefactored.php 和 MenuConfig.php 文件已上传</li>";
echo "<li>如果测试成功，可以将原有调用替换为新版本</li>";
echo "</ol>";

echo "<h2>替换方法</h2>";
echo "<p>将代码中的：</p>";
echo "<code>\$menuData = \\app\\common\\Menu::getdata(\$aid, \$uid);</code>";
echo "<p>替换为：</p>";
echo "<code>\$menuData = \\app\\common\\MenuRefactored::getdata(\$aid, \$uid);</code>";
?>
