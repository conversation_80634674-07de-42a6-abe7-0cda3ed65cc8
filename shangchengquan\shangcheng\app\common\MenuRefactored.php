<?php
/**
 * 重构后的菜单管理类
 * 将原有的巨大Menu类拆分为多个职责明确的类
 */

namespace app\common;

use think\facade\Db;

/**
 * 菜单管理主类
 */
class MenuRefactored
{
    private $userChecker;
    private $menuBuilder;
    private $permissionChecker;

    public function __construct()
    {
        $this->userChecker = new UserPermissionChecker();
        $this->menuBuilder = new MenuBuilder();
        $this->permissionChecker = new PermissionChecker();
    }

    /**
     * 获取菜单数据
     * @param int $aid 应用ID
     * @param int $uid 用户ID
     * @return array
     */
    public static function getdata($aid = 0, $uid = 0)
    {
        $instance = new self();
        return $instance->buildMenu($aid, $uid);
    }

    /**
     * 构建菜单
     */
    private function buildMenu($aid, $uid)
    {
        // 1. 获取用户信息和权限
        $userInfo = $this->userChecker->getUserInfo($aid, $uid);
        
        // 2. 构建基础菜单
        $menuData = $this->menuBuilder->buildMenus($userInfo);
        
        // 3. 应用权限过滤
        $menuData = $this->permissionChecker->filterMenusByPermission($menuData, $userInfo);
        
        return $menuData;
    }

    /**
     * 获取权限白名单
     */
    public static function blacklist()
    {
        return MenuConfig::getBlacklist();
    }
}

/**
 * 用户权限检查器
 */
class UserPermissionChecker
{
    /**
     * 获取用户信息和权限状态
     */
    public function getUserInfo($aid, $uid)
    {
        $user = [];
        $platform = $this->getPlatform($aid);
        
        if ($uid > 0) {
            $user = Db::name('admin_user')->where('id', $uid)->find();
            $isadmin = $this->determineAdminStatus($user, $aid);
        } else {
            $isadmin = $uid == -1 ? false : true;
            if ($uid == -1) {
                $user = Db::name('admin_user')->where('aid', $aid)->where('isadmin', '>', 0)->find();
            }
        }

        // 获取用户类型
        $userType = $this->getUserType($user);

        return [
            'user' => $user,
            'platform' => $platform,
            'isadmin' => $isadmin,
            'userType' => $userType,
            'aid' => $aid,
            'uid' => $uid
        ];
    }

    private function getPlatform($aid)
    {
        if ($aid == 0) {
            return ['mp', 'wx', 'alipay', 'baidu', 'toutiao', 'qq', 'h5', 'app'];
        }
        return explode(',', Db::name('admin')->where('id', $aid)->value('platform'));
    }

    private function determineAdminStatus($user, $aid)
    {
        if ($user['bid'] > 0) {
            $isadmin = false;
            if ($user['auth_type'] == 1) {
                $user = Db::name('admin_user')->where('aid', $aid)->where('isadmin', '>', 0)->find();
            }
        } else {
            $isadmin = true;
        }
        return $isadmin;
    }

    private function getUserType($user)
    {
        return isset($user['type']) ? $user['type'] : 'business';
    }
}

/**
 * 菜单构建器
 */
class MenuBuilder
{
    /**
     * 构建所有菜单
     */
    public function buildMenus($userInfo)
    {
        $menuData = [];
        
        // 商城菜单
        $menuData['shop'] = $this->buildShopMenu($userInfo);
        
        // 会员菜单
        if ($userInfo['isadmin']) {
            $menuData['member'] = $this->buildMemberMenu($userInfo);
        }
        
        // 财务菜单
        $menuData['finance'] = $this->buildFinanceMenu($userInfo);
        
        // 营销菜单
        $menuData['yingxiao'] = $this->buildMarketingMenu($userInfo);
        
        // 拓展功能菜单
        if ($userInfo['isadmin']) {
            $menuData = array_merge($menuData, $this->buildExtensionMenus($userInfo));
        }
        
        // 系统菜单
        $menuData['system'] = $this->buildSystemMenu($userInfo);
        
        return $menuData;
    }

    /**
     * 构建商城菜单
     */
    private function buildShopMenu($userInfo)
    {
        $config = MenuConfig::getShopMenuConfig();
        return $this->processMenuConfig($config, $userInfo);
    }

    /**
     * 构建会员菜单
     */
    private function buildMemberMenu($userInfo)
    {
        $config = MenuConfig::getMemberMenuConfig();
        return $this->processMenuConfig($config, $userInfo);
    }

    /**
     * 构建财务菜单
     */
    private function buildFinanceMenu($userInfo)
    {
        if ($userInfo['isadmin']) {
            $config = MenuConfig::getAdminFinanceMenuConfig();
        } else {
            $config = MenuConfig::getBusinessFinanceMenuConfig($userInfo['userType']);
        }
        return $this->processMenuConfig($config, $userInfo);
    }

    /**
     * 构建营销菜单
     */
    private function buildMarketingMenu($userInfo)
    {
        $config = MenuConfig::getMarketingMenuConfig();
        return $this->processMenuConfig($config, $userInfo);
    }

    /**
     * 构建拓展功能菜单
     */
    private function buildExtensionMenus($userInfo)
    {
        $extensionMenus = [];
        $configs = MenuConfig::getExtensionMenuConfigs();
        
        foreach ($configs as $key => $config) {
            $extensionMenus[$key] = $this->processMenuConfig($config, $userInfo);
        }
        
        return $extensionMenus;
    }

    /**
     * 构建系统菜单
     */
    private function buildSystemMenu($userInfo)
    {
        $config = MenuConfig::getSystemMenuConfig();
        return $this->processMenuConfig($config, $userInfo);
    }

    /**
     * 处理菜单配置
     */
    private function processMenuConfig($config, $userInfo)
    {
        $menu = [
            'name' => $config['name'],
            'fullname' => $config['fullname'],
            'icon' => $config['icon'],
            'child' => []
        ];

        foreach ($config['items'] as $item) {
            if ($this->shouldShowMenuItem($item, $userInfo)) {
                $menuItem = $this->buildMenuItem($item, $userInfo);
                if ($menuItem) {
                    $menu['child'][] = $menuItem;
                }
            }
        }

        return $menu;
    }

    /**
     * 判断是否应该显示菜单项
     */
    private function shouldShowMenuItem($item, $userInfo)
    {
        // 检查管理员权限要求
        if (isset($item['admin_only']) && $item['admin_only'] && !$userInfo['isadmin']) {
            return false;
        }

        // 检查用户类型要求
        if (isset($item['user_types']) && !in_array($userInfo['userType'], $item['user_types'])) {
            return false;
        }

        // 检查隐藏条件
        if (isset($item['hide']) && $item['hide']) {
            return false;
        }

        return true;
    }

    /**
     * 构建单个菜单项
     */
    private function buildMenuItem($item, $userInfo)
    {
        $menuItem = [
            'name' => $item['name'],
            'path' => $item['path'],
            'authdata' => $item['authdata']
        ];

        // 处理隐藏属性
        if (isset($item['hide'])) {
            $menuItem['hide'] = $item['hide'];
        }

        // 处理子菜单
        if (isset($item['children'])) {
            $menuItem['child'] = [];
            foreach ($item['children'] as $child) {
                if ($this->shouldShowMenuItem($child, $userInfo)) {
                    $childItem = $this->buildMenuItem($child, $userInfo);
                    if ($childItem) {
                        $menuItem['child'][] = $childItem;
                    }
                }
            }
            
            // 如果没有子菜单，移除child属性
            if (empty($menuItem['child'])) {
                unset($menuItem['child']);
                return null; // 如果是纯容器菜单且没有子项，则不显示
            }
        }

        return $menuItem;
    }
}

/**
 * 权限检查器
 */
class PermissionChecker
{
    /**
     * 根据用户权限过滤菜单
     */
    public function filterMenusByPermission($menuData, $userInfo)
    {
        $user = $userInfo['user'];
        
        // 如果用户有权限控制
        if ($user && $user['auth_type'] == 0) {
            $authData = $this->getUserAuthData($user);
            $menuData = $this->filterMenusByAuthData($menuData, $authData);
        }

        return $menuData;
    }

    private function getUserAuthData($user)
    {
        if ($user['groupid']) {
            $user['auth_data'] = Db::name('admin_user_group')->where('id', $user['groupid'])->value('auth_data');
        }

        return json_decode($user['auth_data'], true) ?: [];
    }

    private function filterMenusByAuthData($menuData, $authData)
    {
        foreach ($menuData as $k => $v) {
            if (isset($v['child'])) {
                foreach ($v['child'] as $k1 => $v1) {
                    if (!isset($v1['authdata']) && isset($v1['child'])) {
                        // 处理有子菜单但没有authdata的项
                        foreach ($v1['child'] as $k2 => $v2) {
                            if (!in_array($v2['path'] . ',' . $v2['authdata'], $authData)) {
                                unset($menuData[$k]['child'][$k1]['child'][$k2]);
                            }
                        }
                        if (count($menuData[$k]['child'][$k1]['child']) == 0) {
                            unset($menuData[$k]['child'][$k1]);
                        }
                    } else {
                        // 处理普通菜单项
                        if (!in_array($v1['path'] . ',' . $v1['authdata'], $authData)) {
                            unset($menuData[$k]['child'][$k1]);
                        }
                    }
                }
                if (count($menuData[$k]['child']) == 0) {
                    unset($menuData[$k]);
                }
            } else {
                if (!in_array($v['path'] . ',' . $v['authdata'], $authData)) {
                    unset($menuData[$k]);
                }
            }
        }

        return $menuData;
    }
}
