<?php
/**
 * 修复商家后台买单收款菜单显示问题
 * 使用方法：在浏览器中访问此文件，或在命令行中运行 php fix_business_menu.php
 */

// 引入系统配置
define('ROOT_PATH', __DIR__ . '/');
$config = include(ROOT_PATH.'config.php');

echo "<h2>商家后台买单收款菜单修复工具</h2>";

try {
    // 连接数据库
    $pdo = new PDO(
        "mysql:host={$config['hostname']};dbname={$config['database']};charset=utf8mb4",
        $config['username'],
        $config['password']
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h3>1. 检查admin_user表结构</h3>";
    
    // 检查是否有type字段
    $stmt = $pdo->query("SHOW COLUMNS FROM {$config['prefix']}admin_user LIKE 'type'");
    $hasTypeField = $stmt->rowCount() > 0;
    
    if (!$hasTypeField) {
        echo "<p style='color: orange;'>⚠️ 检测到admin_user表缺少type字段，正在添加...</p>";
        $pdo->exec("ALTER TABLE `{$config['prefix']}admin_user` ADD COLUMN `type` VARCHAR(20) DEFAULT 'business' COMMENT '用户类型：business-商家，tuanzhang-团长' AFTER `isadmin`");
        echo "<p style='color: green;'>✓ type字段添加成功</p>";
    } else {
        echo "<p style='color: green;'>✓ admin_user表已有type字段</p>";
    }
    
    echo "<h3>2. 检查商家用户设置</h3>";
    
    // 查询所有商家用户
    $stmt = $pdo->query("SELECT id, username, bid, type, isadmin FROM {$config['prefix']}admin_user WHERE bid > 0");
    $businessUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($businessUsers)) {
        echo "<p style='color: red;'>❌ 没有找到商家用户（bid > 0）</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>用户名</th><th>BID</th><th>类型</th><th>IsAdmin</th><th>状态</th><th>操作</th></tr>";
        
        $needFix = [];
        foreach ($businessUsers as $user) {
            $status = '';
            $action = '';
            
            if (empty($user['type']) || $user['type'] !== 'business') {
                $status = '<span style="color: red;">需要修复</span>';
                $action = '设置为business';
                $needFix[] = $user['id'];
            } else {
                $status = '<span style="color: green;">正常</span>';
                $action = '无需操作';
            }
            
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['bid']}</td>";
            echo "<td>" . ($user['type'] ?: 'NULL') . "</td>";
            echo "<td>{$user['isadmin']}</td>";
            echo "<td>$status</td>";
            echo "<td>$action</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h3>3. 修复用户类型</h3>";
        
        if (!empty($needFix)) {
            $ids = implode(',', $needFix);
            $stmt = $pdo->prepare("UPDATE {$config['prefix']}admin_user SET type = 'business' WHERE id IN ($ids)");
            $stmt->execute();
            
            echo "<p style='color: green;'>✓ 已修复 " . count($needFix) . " 个商家用户的类型设置</p>";
        } else {
            echo "<p style='color: green;'>✓ 所有商家用户类型设置正确</p>";
        }
    }
    
    echo "<h3>4. 验证修复结果</h3>";
    
    // 重新查询验证
    $stmt = $pdo->query("SELECT id, username, bid, type FROM {$config['prefix']}admin_user WHERE bid > 0");
    $verifyUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $allCorrect = true;
    foreach ($verifyUsers as $user) {
        if ($user['type'] !== 'business') {
            $allCorrect = false;
            break;
        }
    }
    
    if ($allCorrect) {
        echo "<p style='color: green;'>✓ 所有商家用户类型设置正确，买单收款菜单应该正常显示</p>";
    } else {
        echo "<p style='color: red;'>❌ 仍有用户类型设置不正确</p>";
    }
    
    echo "<h3>5. 使用说明</h3>";
    echo "<ol>";
    echo "<li>确保商家用户的bid字段大于0</li>";
    echo "<li>确保商家用户的type字段为'business'</li>";
    echo "<li>清除浏览器缓存并重新登录商家后台</li>";
    echo "<li>在财务管理菜单下应该能看到买单收款相关菜单</li>";
    echo "</ol>";
    
    echo "<h3>6. 菜单结构</h3>";
    echo "<p>商家后台买单收款菜单包含：</p>";
    echo "<ul>";
    echo "<li>买单扣费 (BusinessMaidan/add)</li>";
    echo "<li>买单记录 (BusinessMaidan/index)</li>";
    echo "<li>收款码 (BusinessMaidan/set)</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>数据库连接失败: " . $e->getMessage() . "</p>";
    echo "<p>请检查config.php中的数据库配置</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>执行失败: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><small>修复完成后，请删除此文件以确保安全</small></p>";
?>
