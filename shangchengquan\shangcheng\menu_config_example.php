<?php
/**
 * 菜单配置示例
 * 展示如何轻松添加和管理菜单配置
 */

namespace app\common;

/**
 * 扩展菜单配置示例
 * 展示如何在重构后的系统中添加新菜单
 */
class MenuConfigExample
{
    /**
     * 示例：添加新的业务模块菜单
     */
    public static function getCustomBusinessMenuConfig()
    {
        return [
            'name' => '业务管理',
            'fullname' => '自定义业务管理',
            'icon' => 'my-icon my-icon-business',
            'items' => [
                // 基础菜单项
                [
                    'name' => '客户管理',
                    'path' => 'Customer/index',
                    'authdata' => 'Customer/*'
                ],
                
                // 仅管理员可见的菜单
                [
                    'name' => '系统配置',
                    'path' => 'BusinessConfig/index',
                    'authdata' => 'BusinessConfig/*',
                    'admin_only' => true
                ],
                
                // 指定用户类型可见的菜单
                [
                    'name' => '商家专用功能',
                    'path' => 'BusinessSpecial/index',
                    'authdata' => 'BusinessSpecial/*',
                    'user_types' => ['business']
                ],
                
                // 带子菜单的复杂菜单
                [
                    'name' => '订单管理',
                    'children' => [
                        [
                            'name' => '订单列表',
                            'path' => 'Order/index',
                            'authdata' => 'Order/*'
                        ],
                        [
                            'name' => '订单统计',
                            'path' => 'Order/statistics',
                            'authdata' => 'Order/statistics'
                        ],
                        [
                            'name' => '退款管理',
                            'path' => 'Refund/index',
                            'authdata' => 'Refund/*',
                            'admin_only' => true
                        ]
                    ]
                ],
                
                // 隐藏菜单（用于权限控制但不显示）
                [
                    'name' => '隐藏功能',
                    'path' => 'Hidden/action',
                    'authdata' => 'Hidden/*',
                    'hide' => true
                ]
            ]
        ];
    }

    /**
     * 示例：条件性菜单配置
     * 根据不同条件显示不同菜单
     */
    public static function getConditionalMenuConfig($userInfo)
    {
        $items = [];
        
        // 根据用户类型添加不同菜单
        if ($userInfo['userType'] == 'business') {
            $items[] = [
                'name' => '商家中心',
                'path' => 'BusinessCenter/index',
                'authdata' => 'BusinessCenter/*'
            ];
        } elseif ($userInfo['userType'] == 'tuanzhang') {
            $items[] = [
                'name' => '团长中心',
                'path' => 'TuanzhangCenter/index',
                'authdata' => 'TuanzhangCenter/*'
            ];
        }
        
        // 根据权限级别添加菜单
        if ($userInfo['isadmin']) {
            $items[] = [
                'name' => '高级设置',
                'path' => 'AdvancedSettings/index',
                'authdata' => 'AdvancedSettings/*'
            ];
        }
        
        return [
            'name' => '个人中心',
            'fullname' => '个人中心管理',
            'icon' => 'my-icon my-icon-user',
            'items' => $items
        ];
    }

    /**
     * 示例：动态菜单配置
     * 从数据库或其他数据源加载菜单配置
     */
    public static function getDynamicMenuConfig()
    {
        // 这里可以从数据库加载菜单配置
        // $menuItems = Db::name('custom_menu')->where('status', 1)->select();
        
        // 模拟动态菜单数据
        $dynamicItems = [
            [
                'name' => '动态功能1',
                'path' => 'Dynamic/feature1',
                'authdata' => 'Dynamic/*'
            ],
            [
                'name' => '动态功能2',
                'path' => 'Dynamic/feature2',
                'authdata' => 'Dynamic/*'
            ]
        ];
        
        return [
            'name' => '扩展功能',
            'fullname' => '动态扩展功能',
            'icon' => 'my-icon my-icon-extension',
            'items' => $dynamicItems
        ];
    }

    /**
     * 示例：多级子菜单配置
     */
    public static function getMultiLevelMenuConfig()
    {
        return [
            'name' => '多级菜单',
            'fullname' => '多级菜单示例',
            'icon' => 'my-icon my-icon-multilevel',
            'items' => [
                [
                    'name' => '一级菜单1',
                    'children' => [
                        [
                            'name' => '二级菜单1-1',
                            'path' => 'Level2/menu11',
                            'authdata' => 'Level2/*'
                        ],
                        [
                            'name' => '二级菜单1-2',
                            'children' => [
                                [
                                    'name' => '三级菜单1-2-1',
                                    'path' => 'Level3/menu121',
                                    'authdata' => 'Level3/*'
                                ],
                                [
                                    'name' => '三级菜单1-2-2',
                                    'path' => 'Level3/menu122',
                                    'authdata' => 'Level3/*'
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }
}

/**
 * 使用示例
 */
class MenuUsageExample
{
    /**
     * 如何在MenuBuilder中使用自定义配置
     */
    public function buildCustomMenus($userInfo)
    {
        $menuData = [];
        
        // 添加自定义业务菜单
        $customConfig = MenuConfigExample::getCustomBusinessMenuConfig();
        $menuData['custom_business'] = $this->processMenuConfig($customConfig, $userInfo);
        
        // 添加条件性菜单
        $conditionalConfig = MenuConfigExample::getConditionalMenuConfig($userInfo);
        $menuData['personal'] = $this->processMenuConfig($conditionalConfig, $userInfo);
        
        // 添加动态菜单
        $dynamicConfig = MenuConfigExample::getDynamicMenuConfig();
        $menuData['dynamic'] = $this->processMenuConfig($dynamicConfig, $userInfo);
        
        return $menuData;
    }

    /**
     * 如何扩展现有菜单
     */
    public function extendExistingMenu($existingConfig, $userInfo)
    {
        // 在现有配置基础上添加新项目
        $existingConfig['items'][] = [
            'name' => '新增功能',
            'path' => 'NewFeature/index',
            'authdata' => 'NewFeature/*'
        ];
        
        return $existingConfig;
    }

    /**
     * 如何根据配置文件加载菜单
     */
    public function loadMenuFromConfig($configFile)
    {
        // 从JSON配置文件加载
        $configData = json_decode(file_get_contents($configFile), true);
        
        return [
            'name' => $configData['name'],
            'fullname' => $configData['fullname'],
            'icon' => $configData['icon'],
            'items' => $configData['items']
        ];
    }
}

/**
 * 配置文件示例 (menu_config.json)
 */
$jsonConfigExample = [
    'name' => '外部配置',
    'fullname' => '从JSON文件加载的菜单',
    'icon' => 'my-icon my-icon-json',
    'items' => [
        [
            'name' => 'JSON功能1',
            'path' => 'JsonFeature/index',
            'authdata' => 'JsonFeature/*'
        ],
        [
            'name' => 'JSON功能2',
            'path' => 'JsonFeature/feature2',
            'authdata' => 'JsonFeature/*',
            'admin_only' => true
        ]
    ]
];

// 保存配置文件示例
// file_put_contents('menu_config.json', json_encode($jsonConfigExample, JSON_PRETTY_PRINT));

echo "菜单配置示例已创建，展示了以下功能：\n";
echo "1. 基础菜单配置\n";
echo "2. 权限控制菜单\n";
echo "3. 用户类型特定菜单\n";
echo "4. 多级子菜单\n";
echo "5. 条件性菜单\n";
echo "6. 动态菜单加载\n";
echo "7. 外部配置文件支持\n";
?>
