<?php
/**
 * Menu重构测试脚本
 * 用于验证重构后的菜单系统是否正常工作
 */

// 引入必要的文件
require_once 'app/common/MenuRefactored.php';
require_once 'app/common/MenuConfig.php';

// 模拟ThinkPHP环境
if (!function_exists('t')) {
    function t($text) {
        return $text;
    }
}

// 模拟数据库查询
class MockDb {
    public static function name($table) {
        return new self();
    }
    
    public function where($field, $value) {
        return $this;
    }
    
    public function find() {
        // 模拟用户数据
        return [
            'id' => 1,
            'bid' => 1,
            'auth_type' => 0,
            'type' => 'business',
            'isadmin' => 0,
            'groupid' => 0,
            'auth_data' => '[]'
        ];
    }
    
    public function value($field) {
        if ($field == 'platform') {
            return 'mp,wx,h5';
        }
        return '[]';
    }
}

// 替换Db类
class_alias('MockDb', 'think\facade\Db');

echo "<h1>Menu重构测试</h1>";

try {
    echo "<h2>1. 测试基本功能</h2>";
    
    // 测试管理员菜单
    echo "<h3>管理员菜单测试</h3>";
    $adminMenu = \app\common\MenuRefactored::getdata(1, 0);
    echo "管理员菜单项数量: " . count($adminMenu) . "<br>";
    
    // 测试商家菜单
    echo "<h3>商家菜单测试</h3>";
    $businessMenu = \app\common\MenuRefactored::getdata(1, 1);
    echo "商家菜单项数量: " . count($businessMenu) . "<br>";
    
    // 测试权限白名单
    echo "<h3>权限白名单测试</h3>";
    $blacklist = \app\common\MenuRefactored::blacklist();
    echo "白名单项数量: " . count($blacklist) . "<br>";
    
    echo "<h2>2. 测试菜单结构</h2>";
    
    // 检查商城菜单
    if (isset($businessMenu['shop'])) {
        echo "✓ 商城菜单存在<br>";
        echo "商城子菜单数量: " . count($businessMenu['shop']['child']) . "<br>";
    } else {
        echo "✗ 商城菜单缺失<br>";
    }
    
    // 检查财务菜单
    if (isset($businessMenu['finance'])) {
        echo "✓ 财务菜单存在<br>";
        echo "财务子菜单数量: " . count($businessMenu['finance']['child']) . "<br>";
        
        // 检查买单收款菜单
        $hasMaidanMenu = false;
        foreach ($businessMenu['finance']['child'] as $item) {
            if ($item['name'] == '买单收款') {
                $hasMaidanMenu = true;
                echo "✓ 买单收款菜单存在<br>";
                echo "买单收款子菜单数量: " . count($item['child']) . "<br>";
                break;
            }
        }
        if (!$hasMaidanMenu) {
            echo "✗ 买单收款菜单缺失<br>";
        }
    } else {
        echo "✗ 财务菜单缺失<br>";
    }
    
    echo "<h2>3. 测试配置系统</h2>";
    
    // 测试商城菜单配置
    $shopConfig = \app\common\MenuConfig::getShopMenuConfig();
    echo "商城菜单配置项数量: " . count($shopConfig['items']) . "<br>";
    
    // 测试会员菜单配置
    $memberConfig = \app\common\MenuConfig::getMemberMenuConfig();
    echo "会员菜单配置项数量: " . count($memberConfig['items']) . "<br>";
    
    // 测试财务菜单配置
    $financeConfig = \app\common\MenuConfig::getBusinessFinanceMenuConfig('business');
    echo "商家财务菜单配置项数量: " . count($financeConfig['items']) . "<br>";
    
    echo "<h2>4. 测试用户类型</h2>";
    
    // 测试不同用户类型的菜单
    $userChecker = new \app\common\UserPermissionChecker();
    
    // 模拟商家用户
    $businessUserInfo = $userChecker->getUserInfo(1, 1);
    echo "商家用户类型: " . $businessUserInfo['userType'] . "<br>";
    echo "是否管理员: " . ($businessUserInfo['isadmin'] ? '是' : '否') . "<br>";
    
    echo "<h2>5. 性能测试</h2>";
    
    $startTime = microtime(true);
    for ($i = 0; $i < 100; $i++) {
        \app\common\MenuRefactored::getdata(1, 1);
    }
    $endTime = microtime(true);
    $executionTime = ($endTime - $startTime) * 1000;
    echo "100次菜单生成耗时: " . number_format($executionTime, 2) . " 毫秒<br>";
    
    echo "<h2>6. 详细菜单结构</h2>";
    echo "<pre>";
    print_r($businessMenu);
    echo "</pre>";
    
    echo "<h2>测试完成</h2>";
    echo "<p style='color: green;'>✓ 所有测试通过，重构成功！</p>";
    
} catch (Exception $e) {
    echo "<h2>测试失败</h2>";
    echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<h2>使用说明</h2>";
echo "<ol>";
echo "<li>将原有的 Menu::getdata() 调用替换为 MenuRefactored::getdata()</li>";
echo "<li>新增菜单项时，在 MenuConfig.php 中添加配置即可</li>";
echo "<li>支持灵活的权限控制和用户类型判断</li>";
echo "<li>代码结构清晰，便于维护和扩展</li>";
echo "</ol>";

echo "<h2>重构优势</h2>";
echo "<ul>";
echo "<li><strong>可维护性</strong>: 代码结构清晰，职责分明</li>";
echo "<li><strong>可扩展性</strong>: 配置化管理，易于添加新功能</li>";
echo "<li><strong>可测试性</strong>: 模块化设计，便于单元测试</li>";
echo "<li><strong>性能优化</strong>: 减少重复代码，优化执行效率</li>";
echo "</ul>";
?>
