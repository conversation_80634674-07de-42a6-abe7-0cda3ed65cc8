# Menu.php 重构指南

## 重构目标

将原有的1482行巨大Menu类重构为模块化、可维护的架构。

## 问题分析

### 原有代码问题
1. **单一方法过长**：1482行的getdata方法违反了单一职责原则
2. **代码重复**：大量相似的菜单配置逻辑重复
3. **硬编码严重**：菜单配置直接写在代码中，难以维护
4. **结构混乱**：所有逻辑混在一个方法中
5. **可扩展性差**：添加新菜单需要修改核心逻辑

## 重构方案

### 新架构设计

```
MenuRefactored (主入口)
├── UserPermissionChecker (用户权限检查器)
├── MenuBuilder (菜单构建器)
├── PermissionChecker (权限过滤器)
└── MenuConfig (菜单配置)
```

### 核心类说明

#### 1. MenuRefactored
- **职责**：主入口，协调各个组件
- **方法**：
  - `getdata($aid, $uid)` - 获取菜单数据
  - `blacklist()` - 获取权限白名单

#### 2. UserPermissionChecker
- **职责**：用户信息和权限状态检查
- **方法**：
  - `getUserInfo($aid, $uid)` - 获取用户完整信息
  - `getPlatform($aid)` - 获取平台信息
  - `determineAdminStatus($user, $aid)` - 判断管理员状态
  - `getUserType($user)` - 获取用户类型

#### 3. MenuBuilder
- **职责**：根据用户权限构建菜单
- **方法**：
  - `buildMenus($userInfo)` - 构建所有菜单
  - `buildShopMenu($userInfo)` - 构建商城菜单
  - `buildMemberMenu($userInfo)` - 构建会员菜单
  - `buildFinanceMenu($userInfo)` - 构建财务菜单
  - `buildMarketingMenu($userInfo)` - 构建营销菜单
  - `buildSystemMenu($userInfo)` - 构建系统菜单

#### 4. PermissionChecker
- **职责**：根据用户权限过滤菜单
- **方法**：
  - `filterMenusByPermission($menuData, $userInfo)` - 权限过滤
  - `getUserAuthData($user)` - 获取用户权限数据
  - `filterMenusByAuthData($menuData, $authData)` - 按权限数据过滤

#### 5. MenuConfig
- **职责**：集中管理所有菜单配置
- **方法**：
  - `getShopMenuConfig()` - 商城菜单配置
  - `getMemberMenuConfig()` - 会员菜单配置
  - `getAdminFinanceMenuConfig()` - 管理员财务菜单配置
  - `getBusinessFinanceMenuConfig($userType)` - 商家财务菜单配置
  - `getMarketingMenuConfig()` - 营销菜单配置
  - `getSystemMenuConfig()` - 系统菜单配置
  - `getBlacklist()` - 权限白名单

## 使用方法

### 1. 替换原有调用

**原来的调用方式：**
```php
$menuData = \app\common\Menu::getdata($aid, $uid);
```

**新的调用方式：**
```php
$menuData = \app\common\MenuRefactored::getdata($aid, $uid);
```

### 2. 添加新菜单

**在MenuConfig.php中添加配置：**
```php
// 在对应的菜单配置方法中添加
[
    'name' => '新功能',
    'path' => 'NewFeature/index',
    'authdata' => 'NewFeature/*',
    'admin_only' => true  // 可选：仅管理员可见
]
```

### 3. 添加新菜单类型

**1. 在MenuConfig.php中添加配置方法：**
```php
public static function getNewMenuConfig()
{
    return [
        'name' => '新模块',
        'fullname' => '新模块管理',
        'icon' => 'my-icon my-icon-new',
        'items' => [
            // 菜单项配置
        ]
    ];
}
```

**2. 在MenuBuilder.php中添加构建方法：**
```php
private function buildNewMenu($userInfo)
{
    $config = MenuConfig::getNewMenuConfig();
    return $this->processMenuConfig($config, $userInfo);
}
```

**3. 在buildMenus方法中调用：**
```php
$menuData['new'] = $this->buildNewMenu($userInfo);
```

## 配置格式说明

### 菜单项配置格式
```php
[
    'name' => '菜单名称',           // 必需：显示名称
    'path' => 'Controller/action', // 必需：路由路径
    'authdata' => 'Controller/*',  // 必需：权限数据
    'admin_only' => true,          // 可选：仅管理员可见
    'user_types' => ['business'],  // 可选：指定用户类型
    'hide' => true,                // 可选：隐藏菜单
    'children' => [                // 可选：子菜单
        // 子菜单配置
    ]
]
```

### 菜单组配置格式
```php
[
    'name' => '菜单组名称',
    'fullname' => '菜单组完整名称',
    'icon' => 'CSS图标类名',
    'items' => [
        // 菜单项数组
    ]
]
```

## 迁移步骤

### 1. 备份原文件
```bash
cp app/common/Menu.php app/common/Menu.php.backup
```

### 2. 部署新文件
- 上传 `MenuRefactored.php`
- 上传 `MenuConfig.php`

### 3. 测试验证
```php
// 测试脚本
$aid = 1;  // 应用ID
$uid = 1;  // 用户ID，可以改为您的实际用户ID

try {
    echo "开始测试菜单系统...\n";

    // 测试原版本（如果还存在）
    if (class_exists('\app\common\Menu')) {
        echo "测试原版本菜单...\n";
        $oldMenu = \app\common\Menu::getdata($aid, $uid);
        echo "原版本菜单项数量: " . count($oldMenu) . "\n";
    }

    // 测试新版本
    echo "测试重构版本菜单...\n";
    $newMenu = \app\common\MenuRefactored::getdata($aid, $uid);
    echo "重构版本菜单项数量: " . count($newMenu) . "\n";

    // 检查关键菜单是否存在
    $requiredMenus = ['shop', 'finance'];
    foreach ($requiredMenus as $menuKey) {
        if (isset($newMenu[$menuKey])) {
            echo "✓ {$menuKey} 菜单存在\n";
            echo "  子菜单数量: " . count($newMenu[$menuKey]['child']) . "\n";
        } else {
            echo "✗ {$menuKey} 菜单缺失\n";
        }
    }

    // 检查买单收款菜单
    if (isset($newMenu['finance']['child'])) {
        $hasMaidanMenu = false;
        foreach ($newMenu['finance']['child'] as $item) {
            if (strpos($item['name'], '买单') !== false || strpos($item['name'], '收款') !== false) {
                $hasMaidanMenu = true;
                echo "✓ 找到买单收款相关菜单: {$item['name']}\n";
                if (isset($item['child'])) {
                    echo "  包含子菜单: " . count($item['child']) . " 项\n";
                }
                break;
            }
        }
        if (!$hasMaidanMenu) {
            echo "⚠ 未找到买单收款菜单\n";
        }
    }

    echo "测试完成！\n";

} catch (Exception $e) {
    echo "测试失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}
```

### 4. 逐步替换
1. 先在测试环境验证
2. 确认功能正常后替换生产环境
3. 监控错误日志

### 5. 清理旧代码
确认新版本稳定运行后，可以删除原有的Menu.php文件。

## 优势

### 1. 可维护性提升
- 代码结构清晰，职责分明
- 配置与逻辑分离
- 易于定位和修复问题

### 2. 可扩展性增强
- 添加新菜单只需修改配置文件
- 支持灵活的权限控制
- 便于添加新的用户类型

### 3. 可测试性改善
- 各个组件可以独立测试
- 便于编写单元测试
- 降低测试复杂度

### 4. 性能优化
- 减少重复代码执行
- 优化权限检查逻辑
- 支持缓存机制

## 注意事项

1. **兼容性**：新版本保持与原版本的接口兼容
2. **权限控制**：确保权限检查逻辑的正确性
3. **配置完整性**：确保所有原有菜单都已迁移到配置文件
4. **测试覆盖**：充分测试各种用户类型和权限组合

## 后续优化建议

1. **缓存机制**：为菜单数据添加缓存，提升性能
2. **配置文件**：将菜单配置移到独立的配置文件中
3. **国际化支持**：完善多语言支持
4. **动态菜单**：支持从数据库动态加载菜单配置
5. **菜单排序**：添加菜单排序功能
6. **权限继承**：实现更复杂的权限继承机制
